{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=template&id=5888aa98&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}