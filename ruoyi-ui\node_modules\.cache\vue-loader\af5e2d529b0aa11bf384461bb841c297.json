{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=template&id=5888aa98&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}