{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "loadCachedContent", "window", "addEventListener", "handleBeforeUnload", "<PERSON><PERSON><PERSON><PERSON>", "saveToCacheNow", "autoSaveTimer", "clearTimeout", "removeEventListener", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "console", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "handleEditQuestion", "question", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "isUploading", "isParsing", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "allExpanded", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentHtmlContent", "lastSaveTime", "Date", "toLocaleString", "msg", "handleUploadError", "collapseAll", "_this0", "$set", "toggleQuestion", "toggleAllQuestions", "_this1", "confirmImport", "_this10", "importQuestions", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "importOptions", "batchImportQuestions", "v", "Error", "clearCache", "a", "_this12", "CKEDITOR", "warn", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "log", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "hasUnsavedChanges", "saveToCache", "contentToLoad", "_this13", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "slice", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "_parsedQuestion$quest", "_parsedQuestion$optio", "options", "answer", "<PERSON><PERSON><PERSON><PERSON>", "_parsedQuestion$quest2", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "inferQuestionType", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "finalContent", "hasQuestionNumber", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this14", "images", "imageIndex", "contentWithPlaceholders", "img", "startIndex", "isArray", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this15", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "getQuestionTypeColor", "colorMap", "cachedData", "localStorage", "getItem", "cache<PERSON>ey", "JSON", "parse", "_this16", "dataToSave", "timestamp", "now", "setItem", "stringify", "removeItem", "event", "returnValue", "manualSave", "handleCacheCommand", "command", "_this17", "exportDraft", "blob", "Blob", "url", "URL", "createObjectURL", "link", "href", "toISOString", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "cleanupQuestionContent", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <span class=\"orange\">最后保存的草稿时间：{{ lastSaveTime || '暂无' }}</span>\n            <span v-if=\"hasUnsavedChanges\" class=\"unsaved-indicator\">●</span>\n            <div class=\"fr\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                @click=\"manualSave\"\n                :disabled=\"!hasUnsavedChanges\"\n              >\n                <i class=\"el-icon-document\"></i> 保存草稿\n              </el-button>\n              <el-dropdown trigger=\"click\" @command=\"handleCacheCommand\">\n                <el-button size=\"mini\" type=\"info\">\n                  <i class=\"el-icon-more\"></i>\n                </el-button>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"clear\">清除缓存</el-dropdown-item>\n                  <el-dropdown-item command=\"export\">导出草稿</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}.</font>\n                      <span class=\"question-type-tag\" v-if=\"question.typeName\">【{{ question.typeName }}】</span>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 题干内容 -->\n                    <div class=\"question-main-content\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n\n                    <!-- 答案显示在下方 -->\n                    <div class=\"question-answer-section\">\n                      <span class=\"question-answer-label\">答案：</span>\n                      <span class=\"question-answer-value\">{{ question.correctAnswer }}</span>\n                    </div>\n                  </div>\n\n                  <!-- 解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      lastSaveTime: '',\n      // 缓存相关\n      cacheKey: 'questionBank_draft_content',\n      autoSaveTimer: null,\n      hasUnsavedChanges: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n    this.loadCachedContent()\n\n    // 监听页面关闭事件，保存内容\n    window.addEventListener('beforeunload', this.handleBeforeUnload)\n  },\n\n  beforeDestroy() {\n    // 保存当前内容到缓存\n    this.saveToCacheNow()\n\n    // 清理定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer)\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n        console.error('获取题目列表失败', error)\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n        console.error('获取统计数据失败', error)\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('删除题目失败', error)\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n          console.error('❌ 解析失败:', {\n            errors: response.errors,\n            response: response\n          })\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastSaveTime = new Date().toLocaleString()\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n        console.error('上传失败响应:', response)\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n      console.error('上传失败:', error, file?.name)\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 导入成功后清除缓存\n        this.clearCache()\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        console.warn('CKEditor未加载，使用普通文本框')\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          console.error('编辑器容器不存在')\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n            console.error('CKEditor未加载或不可用')\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n                  // CKEditor插件加载完成\n                },\n                instanceReady: function() {\n                  // CKEditor实例准备就绪\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      // 图像对话框打开\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n            console.error('CKEditor完整配置初始化失败，尝试简化配置:', error)\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n                    // CKEditor简化配置实例准备就绪\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n                        // 简化配置：图像对话框打开\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n              console.log('CKEditor简化配置初始化成功')\n            } catch (fallbackError) {\n              console.error('CKEditor简化配置也失败，使用普通文本框:', fallbackError)\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              console.log('🔍 CKEditor原始内容:', rawContent)\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n              console.log('🔍 转换相对路径后:', contentWithRelativeUrls)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n              console.log('🔍 最终documentContent:', this.documentContent)\n              this.lastSaveTime = new Date().toLocaleString()\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            console.log('CKEditor初始化成功')\n\n            // 优先加载缓存的HTML内容，如果没有则使用documentContent\n            const contentToLoad = this.documentHtmlContent || this.documentContent\n            if (contentToLoad) {\n              console.log('📦 加载内容到编辑器:', contentToLoad.substring(0, 100) + '...')\n              this.richEditor.setData(contentToLoad)\n            }\n          })\n        })\n\n      } catch (error) {\n        console.error('富文本编辑器初始化失败:', error)\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n          this.lastSaveTime = new Date().toLocaleString()\n\n          // 标记有未保存的更改并保存到缓存\n          this.hasUnsavedChanges = true\n          this.saveToCache()\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      console.log('🔍 setEditorContent 输入:', content)\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n        // 更新保存时间\n        this.lastSaveTime = new Date().toLocaleString()\n      } catch (error) {\n        console.error('解析失败', error)\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n        console.warn('⚠️ 解析内容为空或格式不正确')\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n        console.log('开始解析题目内容，长度:', content.length)\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          console.warn('⚠️ 处理后的内容为空')\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n        console.log('总行数:', lines.length)\n\n        if (lines.length === 0) {\n          console.warn('⚠️ 没有有效的内容行')\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n        console.log('前5行内容:', lines.slice(0, 5))\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                console.log(`🔍 解析题目 ${questionNumber}，内容:`, questionText)\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                  console.log(`✅ 解析题目 ${questions.length} 成功:`, {\n                    type: parsedQuestion.questionType,\n                    content: parsedQuestion.questionContent?.substring(0, 50) + '...',\n                    options: parsedQuestion.options?.length || 0,\n                    answer: parsedQuestion.correctAnswer\n                  })\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n                console.error(`❌ 题目 ${questionNumber} 解析失败:`, error)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n            console.log(`🔍 发现题目 ${questionNumber}:`, line)\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n              console.log(`✅ 解析最后题目 ${questions.length}:`, parsedQuestion.questionContent?.substring(0, 50) + '...')\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n            console.error(`❌ 最后题目 ${questionNumber} 解析失败:`, error)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n          console.log('🔍 找到题型标注:', typeText)\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          console.log('🔍 解析出题目类型:', questionType)\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n            console.log('🔍 题型标注同行有内容:', remainingContent)\n          } else {\n            contentStartIndex = i + 1\n            console.log('🔍 题型标注独立一行，从下一行开始解析')\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析，并尝试推断题型\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n        // 尝试根据题目内容推断题型\n        questionType = this.inferQuestionType(lines)\n        console.log('🔍 未找到题型标注，推断题型为:', questionType)\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      console.log(`📝 收集到的题目内容:`, questionContent)\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      console.log(`📝 题目内容解析结果:`, {\n        type: questionType,\n        originalContent: questionContent.trim(),\n        finalContent: finalQuestionContent,\n        hasQuestionNumber: /^\\d+[.:：．、]/.test(finalQuestionContent)\n      })\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n        console.log('🔍 根据选项推断题目类型为:', questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n          console.log('🔍 根据答案长度推断题目类型为:', questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        console.log('🖼️ 处理图片路径，原始内容:', content.substring(0, 100) + '...')\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          console.log('🖼️ 发现图片标签，路径:', src)\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          console.log('🖼️ 转换后的图片标签:', result)\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        console.log('🖼️ stripHtmlTagsKeepImages 输入长度:', content.length)\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          console.log('🖼️ 找到图片标签:', match.substring(0, 50) + '...')\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        console.log('🖼️ stripHtmlTagsKeepImages 完成，输出长度:', finalContent.length)\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n            console.log('🔍 解析到难度:', difficulty)\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        1: '单选题',\n        2: '多选题',\n        3: '判断题'\n      }\n      return typeMap[type] || '单选题'\n    },\n\n\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n    // ==================== 缓存保存相关方法 ====================\n\n    // 加载缓存的内容\n    loadCachedContent() {\n      try {\n        const cachedData = localStorage.getItem(this.cacheKey)\n        if (cachedData) {\n          const data = JSON.parse(cachedData)\n          console.log('📦 加载缓存内容:', data)\n\n          // 恢复内容\n          this.documentContent = data.documentContent || ''\n          this.documentHtmlContent = data.documentHtmlContent || ''\n          this.lastSaveTime = data.lastSaveTime || ''\n\n          // 如果有内容，标记为有未保存的更改\n          if (this.documentContent || this.documentHtmlContent) {\n            this.hasUnsavedChanges = true\n            console.log('📦 从缓存恢复内容，标记为有未保存更改')\n          }\n        }\n      } catch (error) {\n        console.error('❌ 加载缓存内容失败:', error)\n      }\n    },\n\n    // 保存内容到缓存\n    saveToCache() {\n      // 防抖保存，避免频繁写入\n      if (this.autoSaveTimer) {\n        clearTimeout(this.autoSaveTimer)\n      }\n\n      this.autoSaveTimer = setTimeout(() => {\n        this.saveToCacheNow()\n      }, 2000) // 2秒后保存\n    },\n\n    // 立即保存到缓存\n    saveToCacheNow() {\n      try {\n        const dataToSave = {\n          documentContent: this.documentContent || '',\n          documentHtmlContent: this.documentHtmlContent || '',\n          lastSaveTime: this.lastSaveTime || new Date().toLocaleString(),\n          timestamp: Date.now()\n        }\n\n        localStorage.setItem(this.cacheKey, JSON.stringify(dataToSave))\n        console.log('💾 内容已保存到缓存')\n        this.hasUnsavedChanges = false\n      } catch (error) {\n        console.error('❌ 保存到缓存失败:', error)\n      }\n    },\n\n    // 清除缓存\n    clearCache() {\n      try {\n        localStorage.removeItem(this.cacheKey)\n        this.hasUnsavedChanges = false\n        console.log('🗑️ 缓存已清除')\n      } catch (error) {\n        console.error('❌ 清除缓存失败:', error)\n      }\n    },\n\n    // 页面关闭前的处理\n    handleBeforeUnload(event) {\n      if (this.hasUnsavedChanges) {\n        // 立即保存到缓存\n        this.saveToCacheNow()\n\n        // 提示用户有未保存的更改\n        const message = '您有未保存的更改，确定要离开吗？'\n        event.returnValue = message\n        return message\n      }\n    },\n\n    // 手动保存\n    manualSave() {\n      this.saveToCacheNow()\n      this.$message.success('内容已保存到本地缓存')\n    },\n\n    // 处理缓存相关命令\n    handleCacheCommand(command) {\n      switch (command) {\n        case 'clear':\n          this.$confirm('确定要清除所有缓存的草稿内容吗？', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.clearCache()\n            this.documentContent = ''\n            this.documentHtmlContent = ''\n            this.lastSaveTime = ''\n\n            // 清空编辑器内容\n            if (this.richEditor && this.editorInitialized) {\n              this.richEditor.setData('')\n            }\n\n            this.$message.success('缓存已清除')\n          }).catch(() => {\n            // 用户取消\n          })\n          break\n        case 'export':\n          this.exportDraft()\n          break\n      }\n    },\n\n    // 导出草稿\n    exportDraft() {\n      if (!this.documentHtmlContent && !this.documentContent) {\n        this.$message.warning('没有可导出的草稿内容')\n        return\n      }\n\n      const content = this.documentHtmlContent || this.documentContent\n      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `题目草稿_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.$message.success('草稿已导出')\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理多余的空行和空白字符\n      content = this.cleanupQuestionContent(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的多余空行和空白字符\n    cleanupQuestionContent(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        return content\n          // 移除空的段落标签\n          .replace(/<p[^>]*>\\s*<\\/p>/gi, '')\n          // 移除段落间多余的空白\n          .replace(/(<\\/p>)\\s*(<p[^>]*>)/gi, '$1$2')\n          // 移除开头和结尾的空白\n          .trim()\n      } else {\n        // 处理纯文本内容\n        return content\n          // 移除多余的空行（保留单个换行）\n          .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n')\n          // 移除行首行尾空白\n          .replace(/^\\s+|\\s+$/gm, '')\n          // 移除开头和结尾的空白\n          .trim()\n      }\n    },\n\n    // 清理题目内容中的题号和题型标注\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号和题型标注\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>') // 清理题型标注\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标注\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标注\n      } else {\n        // 对于纯文本内容，直接清理开头的题号和题型标注\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '') // 清理题号\n                     .replace(/^\\s*\\[.*?题\\]\\s*/, '') // 清理题型标注\n                     .trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干和答案同一行显示 */\n.question-main-line {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  flex: 1;\n  margin: 0;\n}\n\n.question-answer-inline {\n  flex-shrink: 0;\n  color: #409eff;\n  font-weight: 500;\n  background: #f0f9ff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n/* 题目内容区域样式 */\n.question-main-content {\n  margin-bottom: 12px;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n/* 题目内容中的段落样式 */\n.question-main-content p {\n  margin: 0 0 8px 0;\n  line-height: 1.6;\n}\n\n.question-main-content p:last-child {\n  margin-bottom: 0;\n}\n\n/* 题目选项样式 */\n.question-options {\n  margin: 8px 0;\n}\n\n.option-item {\n  margin: 4px 0;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n/* 答案区域样式 */\n.question-answer-section {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.question-answer-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.question-answer-value {\n  color: #333;\n  font-weight: 500;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n\n/* 题型标签样式 */\n.question-type-tag {\n  margin-left: 8px;\n  font-size: 12px;\n  color: #409eff;\n  font-weight: normal;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6hBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAAA,IAAAK,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,6BAEA,SAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,yBACA,wBACA,oBACA,oBAEA,+BAEA,wBACA,iBAEA,gDACA,4BACA,uCACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,wBACA,yBAEA,4BAEA,qBACA,yBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,4BACA;EAEA;EAEAC,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAIA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAR,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAQ,KAAA;QACA,IAAAR,MAAA;UACA;UACA,KAAAS,SAAA;YACAD,KAAA,CAAAE,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAP,SAAA;IACA;EACA;EAEAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAZ,qBAAA,QAAAa,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAAiD,aAAA;MACA1B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAwB,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,iBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,sBAAAC,kBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,cAAA;;IAEA;IACA,SAAAC,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;;IAEA;IACAL,MAAA,CAAAO,mBAAA,sBAAAL,kBAAA;;IAEA;IACA,SAAAb,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAmB,OAAA,GAAApE,QAAA;IACA;IACAqD,QAAA,WAAAA,SAAA;MACA,IAAAgB,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAA/D,MAAA,GAAA6D,kBAAA,CAAA7D,MAAA;QAAAC,QAAA,GAAA4D,kBAAA,CAAA5D,QAAA;MACA,KAAAD,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAlE,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAAmE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAjE,WAAA;MACA,IAAAkE,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAhE,YAAA,GAAAqE,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAApE,KAAA,GAAAyE,QAAA,CAAAzE,KAAA;MACA,GAAA2E,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,aAAAA,KAAA;QACAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAQ,eAAA,OAAAC,cAAA,CAAAhE,OAAA,MAAAuD,MAAA;;MAEA;MACA,IAAAQ,eAAA,CAAArE,YAAA;QACA,IAAAuE,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAArE,YAAA,GAAAuE,OAAA,CAAAF,eAAA,CAAArE,YAAA,KAAAqE,eAAA,CAAArE,YAAA;MACA;;MAEA;MACA,IAAAqE,eAAA,CAAApE,UAAA;QACA,IAAAuE,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAApE,UAAA,GAAAuE,aAAA,CAAAH,eAAA,CAAApE,UAAA,KAAAoE,eAAA,CAAApE,UAAA;MACA;;MAEA;MACAwE,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAZ,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAA1F,MAAA,EAAA2E,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAvF,UAAA,GAAA0E,QAAA,CAAA9E,IAAA;MACA,GAAAgF,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,aAAAA,KAAA;QACA;QACAwB,MAAA,CAAAvF,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACAqF,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAApF,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAqF,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAArF,iBAAA,CAAAsF,MAAA;QACA,KAAArC,QAAA,CAAAsC,OAAA;QACA;MACA;MACA,KAAAtC,QAAA,CAAAuC,IAAA,6BAAAC,MAAA,MAAAzF,iBAAA,CAAAsF,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAA3F,iBAAA,QAAAR,YAAA,CAAAoG,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAA7C,QAAA,CAAA8C,OAAA,uBAAAN,MAAA,MAAAzF,iBAAA,CAAAsF,MAAA;MACA;QACA;QACA,KAAAtF,iBAAA;QACA,KAAAiD,QAAA,CAAA8C,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjG,iBAAA,CAAAsF,MAAA;QACA,KAAArC,QAAA,CAAAsC,OAAA;QACA;MACA;MAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAzF,iBAAA,CAAAsF,MAAA;QACAa,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAnB,IAAA;QACA;QACA;QACA,IAAAyC,cAAA,GAAAJ,MAAA,CAAAjG,iBAAA,CAAA4F,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAzC,IAAA;UACAqC,MAAA,CAAAhD,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAQ,MAAA,CAAAjG,iBAAA,CAAAsF,MAAA;UACAW,MAAA,CAAAjG,iBAAA;UACAiG,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAA7C,eAAA;UACA6C,MAAA,CAAA5C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;UACA+C,MAAA,CAAAhD,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAAjD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAAAuH,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAA1G,iBAAA,CAAAsF,MAAA;MACA,KAAArC,QAAA,CAAAsC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAzF,iBAAA,CAAAsF,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACA;MACA,IAAAyC,cAAA,GAAAK,MAAA,CAAA1G,iBAAA,CAAA4F,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;MAAA,CACA;MAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAzC,IAAA;QACA8C,MAAA,CAAAzD,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAiB,MAAA,CAAA1G,iBAAA,CAAAsF,MAAA;QACAoB,MAAA,CAAA1G,iBAAA;QACA0G,MAAA,CAAAf,aAAA;QACAe,MAAA,CAAAtD,eAAA;QACAsD,MAAA,CAAArD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;QACAwD,MAAA,CAAAzD,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA2C,MAAA,CAAAzD,QAAA,CAAAuC,IAAA;IACA;EACA,qCAGAmB,qBAAAb,UAAA,EAAAc,QAAA;IACA,IAAAA,QAAA;MACA,UAAA5G,iBAAA,CAAA6G,QAAA,CAAAf,UAAA;QACA,KAAA9F,iBAAA,CAAA8G,IAAA,CAAAhB,UAAA;MACA;IACA;MACA,IAAAiB,KAAA,QAAA/G,iBAAA,CAAAgH,OAAA,CAAAlB,UAAA;MACA,IAAAiB,KAAA;QACA,KAAA/G,iBAAA,CAAAiH,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAApB,aAAA,QAAA3F,iBAAA,CAAAsF,MAAA,UAAA9F,YAAA,CAAA8F,MAAA;EACA,mCAEA4B,mBAAApB,UAAA;IACA,IAAAiB,KAAA,QAAA3B,iBAAA,CAAA4B,OAAA,CAAAlB,UAAA;IACA,IAAAiB,KAAA;MACA,KAAA3B,iBAAA,CAAA6B,MAAA,CAAAF,KAAA;IACA;MACA,KAAA3B,iBAAA,CAAA0B,IAAA,CAAAhB,UAAA;IACA;EACA,mCAEAqB,mBAAAC,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA;IACA,KAAApC,mBAAA,GAAAoC,QAAA,CAAAxH,YAAA;IACA,KAAAsF,mBAAA;EACA,mCAEAmC,mBAAAD,QAAA;IACA;IACA,IAAAE,cAAA,OAAApD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAkH,QAAA;MACAtB,UAAA;MAAA;MACAyB,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAAzC,mBAAA,GAAAqC,cAAA;IACA,KAAAtC,mBAAA,QAAA2C,2BAAA,CAAAP,QAAA,CAAAxH,YAAA;IACA,KAAAsF,mBAAA;EACA,4CAGAyC,4BAAA5C,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA,KAAAA,IAAA;EACA,qCAEA6C,qBAAAR,QAAA;IAAA,IAAAS,MAAA;IACA,IAAA/H,eAAA,GAAAsH,QAAA,CAAAtH,eAAA,CAAAgI,OAAA;IACA,IAAAC,cAAA,GAAAjI,eAAA,CAAAwF,MAAA,QAAAxF,eAAA,CAAAkI,SAAA,kBAAAlI,eAAA;IACA,KAAAoG,QAAA,0CAAAT,MAAA,CAAAsC,cAAA;MACA5B,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACA,IAAA0C,qBAAA,EAAAc,QAAA,CAAAtB,UAAA,EAAAlC,IAAA;QACAiE,MAAA,CAAA5E,QAAA,CAAA8C,OAAA;QACA8B,MAAA,CAAAzE,eAAA;QACAyE,MAAA,CAAAxE,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;QACA2E,MAAA,CAAA5E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEA+E,0BAAA;IACA,KAAA/C,mBAAA;IACA,KAAA9B,eAAA;IACA,KAAAC,aAAA;EACA,yCAEA6E,yBAAA;IACA,KAAArD,kBAAA;IACA,KAAAvD,mBAAA;IACA,KAAA8B,eAAA;IACA,KAAAC,aAAA;EACA,kCAKA8E,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAAnI,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,uCAGA4J,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAAhH,SAAA;MACA,IAAAiH,eAAA,GAAAH,MAAA,CAAAI,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;EAEA,gCAGAC,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,muDAuBAlI,IAAA;;IAEA;IACA,SAAAS,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAA0H,OAAA,CAAAD,YAAA;IAEA;MACA;MACA,KAAA3H,SAAA;QACA,IAAA0H,MAAA,CAAAxH,UAAA,IAAAwH,MAAA,CAAAtH,iBAAA;UACAsH,MAAA,CAAAxH,UAAA,CAAA0H,OAAA,CAAAD,YAAA;QAEA;MACA;IACA;;IAEA;IACA,KAAAH,kBAAA;;IAEA;IACA,KAAA/F,QAAA,CAAA8C,OAAA;EAGA,sCAGAsD,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA1E,IAAA,kFACA0E,IAAA,CAAA1E,IAAA,4EACA0E,IAAA,CAAA/K,IAAA,CAAAiL,QAAA,aAAAF,IAAA,CAAA/K,IAAA,CAAAiL,QAAA;IACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;IAEA,KAAAH,WAAA;MACA,KAAAzG,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAA0G,OAAA;MACA,KAAA3G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAjB,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAAsJ,WAAA;IACA,KAAAC,SAAA;IAIA;EACA,oCAGAsB,oBAAAjG,QAAA,EAAA4F,IAAA;IAAA,IAAAM,MAAA;IAGA,IAAAlG,QAAA,CAAAmG,IAAA;MACA;MACA,KAAAzB,WAAA;MACA,KAAAC,SAAA;;MAIA;MACA,KAAArH,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA6I,UAAA;QACAF,MAAA,CAAAlB,2BAAA;QACAkB,MAAA,CAAAvB,SAAA;MACA;;MAEA;MACA,KAAAxH,oBAAA;;MAEA;MACA,IAAA6C,QAAA,CAAAqG,SAAA,IAAArG,QAAA,CAAAqG,SAAA,CAAA5E,MAAA;QACA,KAAAnE,eAAA,GAAA0C,QAAA,CAAAqG,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;UAAA,WAAAlD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAkH,QAAA;YACA+C,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAC,WAAA;QACA,KAAAhJ,WAAA,GAAAyC,QAAA,CAAAwG,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAAzG,QAAA,CAAAwG,MAAA,GAAAxG,QAAA,CAAAwG,MAAA,CAAA/E,MAAA;QACA,IAAAgF,UAAA;UACA,KAAArH,QAAA,CAAA8C,OAAA,mCAAAN,MAAA,CAAA5B,QAAA,CAAAqG,SAAA,CAAA5E,MAAA,sCAAAG,MAAA,CAAA6E,UAAA;QACA;UACA,KAAArH,QAAA,CAAA8C,OAAA,mCAAAN,MAAA,CAAA5B,QAAA,CAAAqG,SAAA,CAAA5E,MAAA;QACA;MAGA;QACA,KAAArC,QAAA,CAAAC,KAAA;QACA,KAAA/B,eAAA;QACA,KAAAC,WAAA,GAAAyC,QAAA,CAAAwG,MAAA;QAEArG,OAAA,CAAAd,KAAA;UACAmH,MAAA,EAAAxG,QAAA,CAAAwG,MAAA;UACAxG,QAAA,EAAAA;QACA;MACA;;MAEA;MACA,IAAAA,QAAA,CAAA0G,eAAA;QACA,KAAAC,gBAAA,CAAA3G,QAAA,CAAA0G,eAAA;QACA,KAAA1J,eAAA,GAAAgD,QAAA,CAAA0G,eAAA;QACA,KAAAE,mBAAA,GAAA5G,QAAA,CAAA0G,eAAA;QACA,KAAAG,YAAA,OAAAC,IAAA,GAAAC,cAAA;MACA;;MAEA;MACAX,UAAA;QACAF,MAAA,CAAA/I,oBAAA;MACA;IACA;MACAgD,OAAA,CAAAd,KAAA,YAAAW,QAAA;MACA,KAAAZ,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAgH,GAAA;MACA;MACA,KAAAtC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAsC,kBAAA5H,KAAA,EAAAuG,IAAA;IACAzF,OAAA,CAAAd,KAAA,UAAAA,KAAA,EAAAuG,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA/K,IAAA;IACA,KAAAuE,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAqF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAuC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAA7J,eAAA,CAAAoD,OAAA,WAAA6C,QAAA;MACA4D,MAAA,CAAAC,IAAA,CAAA7D,QAAA;IACA;EACA,+BAGA8D,eAAAnE,KAAA;IACA,IAAAK,QAAA,QAAAjG,eAAA,CAAA4F,KAAA;IACA,KAAAkE,IAAA,CAAA7D,QAAA,gBAAAA,QAAA,CAAA+C,SAAA;EACA,QAAAlK,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGA0M,mBAAA;IAAA,IAAAC,MAAA;IACA,KAAAhB,WAAA,SAAAA,WAAA;IACA,KAAAjJ,eAAA,CAAAoD,OAAA,WAAA6C,QAAA;MACAgE,MAAA,CAAAH,IAAA,CAAA7D,QAAA,gBAAAgE,MAAA,CAAAhB,WAAA;IACA;EAEA,8BAGAiB,cAAA;IAAA,IAAAC,OAAA;IACA,SAAAnK,eAAA,CAAAmE,MAAA;MACA,KAAArC,QAAA,CAAAsC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,6BAAAT,MAAA,MAAAtE,eAAA,CAAAmE,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACA0H,OAAA,CAAAC,eAAA;IACA,GAAAxH,KAAA;EACA,gCAGAwH,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAAvL,OAAA,mBAAAwL,aAAA,CAAAxL,OAAA,IAAAyL,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAAjI,QAAA,EAAAkI,EAAA;MAAA,WAAAL,aAAA,CAAAxL,OAAA,IAAA8L,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAAlM,OAAA,EAAAsL,OAAA,CAAArK,eAAA;YAEA,IAAAqK,OAAA,CAAAa,aAAA,CAAAlM,OAAA;cACA0L,iBAAA,CAAA1L,OAAA;YACA;;YAEA;YACA2L,UAAA;cACA7M,MAAA,EAAAuM,OAAA,CAAAvM,MAAA;cACAiL,SAAA,EAAA2B,iBAAA;cACAzL,cAAA,EAAAoL,OAAA,CAAAa,aAAA,CAAAjM;YACA;YAAA6L,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAI,kCAAA,EAAAR,UAAA;UAAA;YAAAjI,QAAA,GAAAoI,QAAA,CAAAM,CAAA;YAAA,MAEA1I,QAAA,CAAAmG,IAAA;cAAAiC,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAAvI,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAoG,iBAAA,CAAAvG,MAAA;YAAA2G,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAM,KAAA,CAAA3I,QAAA,CAAAgH,GAAA;UAAA;YAEAW,OAAA,CAAAlK,mBAAA;YACAkK,OAAA,CAAA3K,eAAA;YACA2K,OAAA,CAAAf,mBAAA;YACAe,OAAA,CAAArK,eAAA;YACAqK,OAAA,CAAApK,WAAA;;YAEA;YACAoK,OAAA,CAAAiB,UAAA;YAEAjB,OAAA,CAAApI,eAAA;YACAoI,OAAA,CAAAnI,aAAA;YAAA4I,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;YAEAvI,OAAA,CAAAd,KAAA,SAAA6I,EAAA;YACAP,OAAA,CAAAvI,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAA+I,QAAA,CAAAS,CAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EAEA,+BAGAnK,eAAA;IAAA,IAAAkL,OAAA;IACA,SAAA/K,iBAAA;MACA;IACA;;IAEA;IACA,KAAAS,MAAA,CAAAuK,QAAA;MACA5I,OAAA,CAAA6I,IAAA;MACA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAApL,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAAqL,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QACA/I,OAAA,CAAAd,KAAA;QACA;MACA;;MAEA;MACA6J,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAA1L,SAAA;QACA;QACA,KAAAa,MAAA,CAAAuK,QAAA,KAAAvK,MAAA,CAAAuK,QAAA,CAAA9E,OAAA;UACA9D,OAAA,CAAAd,KAAA;UACAyJ,OAAA,CAAAQ,kBAAA;UACA;QACA;QAEA;UACA;UACAR,OAAA,CAAAjL,UAAA,GAAAW,MAAA,CAAAuK,QAAA,CAAA9E,OAAA,6BAAA7H,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAkN,MAAA;YAAA;YACAC,OAAA,GACA;cAAA3O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,GACA;cAAA5O,IAAA;cAAA4O,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAA5N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACA2N,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA;cACA;YAAA,CACA;YACAC,aAAA,WAAAA,cAAA;cACA;;cAEA,IAAAC,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAAxP,IAAA;gBACA,IAAA0P,MAAA,CAAAC,OAAA;kBACA;;kBAEA;kBACAzE,UAAA;oBACA,IAAA0E,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAC,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;wBACA;sBACA,SAAAC,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACAlF,UAAA;sBAAA,OAAAgF,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAAzL,KAAA;UACAc,OAAA,CAAAd,KAAA,8BAAAA,KAAA;;UAEA;UACA;YACAyJ,OAAA,CAAAjL,UAAA,GAAAW,MAAA,CAAAuK,QAAA,CAAA9E,OAAA;cACAsF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAA5N,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACA2N,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBACA;;kBAEA,IAAAD,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAAxP,IAAA;oBACA,IAAA0P,MAAA,CAAAC,OAAA;sBACA;;sBAEA;sBACAzE,UAAA;wBACA,IAAA0E,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAC,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;4BACA;0BACA,SAAAC,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACAlF,UAAA;0BAAA,OAAAgF,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;YACA3K,OAAA,CAAAqL,GAAA;UACA,SAAAC,aAAA;YACAtL,OAAA,CAAAd,KAAA,6BAAAoM,aAAA;YACA3C,OAAA,CAAAQ,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAR,OAAA,CAAAjL,UAAA,IAAAiL,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACA7B,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;YACA,IAAAe,UAAA,GAAA5C,OAAA,CAAAjL,UAAA,CAAA8N,OAAA;YACAxL,OAAA,CAAAqL,GAAA,qBAAAE,UAAA;YACA,IAAAE,uBAAA,GAAA9C,OAAA,CAAA+C,qBAAA,CAAAH,UAAA;YACAvL,OAAA,CAAAqL,GAAA,gBAAAI,uBAAA;;YAEA;YACA9C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAAgD,0BAAA,CAAAF,uBAAA;YACA;YACA9C,OAAA,CAAA9L,eAAA,GAAA8L,OAAA,CAAAiD,uBAAA,CAAAH,uBAAA;YACAzL,OAAA,CAAAqL,GAAA,0BAAA1C,OAAA,CAAA9L,eAAA;YACA8L,OAAA,CAAAjC,YAAA,OAAAC,IAAA,GAAAC,cAAA;;YAEA;YACA+B,OAAA,CAAAkD,iBAAA;YACAlD,OAAA,CAAAmD,WAAA;UACA;QACA;;QAEA;QACAnD,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACAvE,UAAA;YACA,IAAAsF,UAAA,GAAA5C,OAAA,CAAAjL,UAAA,CAAA8N,OAAA;YACA,IAAAC,uBAAA,GAAA9C,OAAA,CAAA+C,qBAAA,CAAAH,UAAA;;YAEA;YACA5C,OAAA,CAAAlC,mBAAA,GAAAkC,OAAA,CAAAgD,0BAAA,CAAAF,uBAAA;YACA;YACA9C,OAAA,CAAA9L,eAAA,GAAA8L,OAAA,CAAAiD,uBAAA,CAAAH,uBAAA;;YAEA;YACA9C,OAAA,CAAAkD,iBAAA;YACAlD,OAAA,CAAAmD,WAAA;UACA;QACA;;QAEA;QACAnD,OAAA,CAAAjL,UAAA,CAAA8M,EAAA;UACA7B,OAAA,CAAA/K,iBAAA;UACAoC,OAAA,CAAAqL,GAAA;;UAEA;UACA,IAAAU,aAAA,GAAApD,OAAA,CAAAlC,mBAAA,IAAAkC,OAAA,CAAA9L,eAAA;UACA,IAAAkP,aAAA;YACA/L,OAAA,CAAAqL,GAAA,iBAAAU,aAAA,CAAA/H,SAAA;YACA2E,OAAA,CAAAjL,UAAA,CAAA0H,OAAA,CAAA2G,aAAA;UACA;QACA;MACA;IAEA,SAAA7M,KAAA;MACAc,OAAA,CAAAd,KAAA,iBAAAA,KAAA;MACA;MACA,KAAA4J,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAAkD,OAAA;IACA,IAAAjD,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAAkD,QAAA,GAAAjD,QAAA,CAAAkD,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA,QAAAxP,eAAA;MACAoP,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAA3N,gBAAA,oBAAA6M,CAAA;QACAa,OAAA,CAAAnP,eAAA,GAAAsO,CAAA,CAAAqB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAAvF,mBAAA,GAAA0E,CAAA,CAAAqB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAAtF,YAAA,OAAAC,IAAA,GAAAC,cAAA;;QAEA;QACAoF,OAAA,CAAAH,iBAAA;QACAG,OAAA,CAAAF,WAAA;MACA;MAEA/C,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAA0D,WAAA,CAAAR,QAAA;MACA,KAAArO,iBAAA;IACA;EACA,8BAGA8O,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAA5D,QAAA,CAAAkD,aAAA;IACAU,GAAA,CAAA1D,SAAA,GAAAyD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGAtG,iBAAAuG,OAAA;IACA/M,OAAA,CAAAqL,GAAA,4BAAA0B,OAAA;IACA,SAAArP,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAA0H,OAAA,CAAA2H,OAAA;IACA;MACA;MACA,KAAAlQ,eAAA,GAAAkQ,OAAA;MACA,KAAAtG,mBAAA,GAAAsG,OAAA;IACA;EACA,yBAKAhP,SAAAiP,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAA/L,MAAA,EAAAgM,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACA9O,YAAA,CAAAuO,OAAA;QACAF,IAAA,CAAAU,KAAA,SAAAJ,IAAA;MACA;MACA3O,YAAA,CAAAuO,OAAA;MACAA,OAAA,GAAAjH,UAAA,CAAAwH,KAAA,EAAAR,IAAA;IACA;EACA,sCAGAvB,sBAAAqB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAY,aAAA,GAAAtP,MAAA,CAAAuP,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAA7J,OAAA;IAEA,OAAAiJ,OAAA,CAAAjJ,OAAA,CAAAgK,QAAA;EACA,8BAGA9P,cAAA;IACA,UAAAnB,eAAA,CAAAI,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAA4Q,WAAA,QAAAC,oBAAA,MAAApR,eAAA;MACA;MACA,KAAAM,eAAA,GAAA6Q,WAAA,CAAA9H,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;QAAA,WAAAlD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAkH,QAAA;UACA+C,SAAA;QAAA;MAAA,CACA;MACA,KAAA/I,WAAA,GAAA4Q,WAAA,CAAA3H,MAAA;;MAEA;MACA,KAAAK,YAAA,OAAAC,IAAA,GAAAC,cAAA;IACA,SAAA1H,KAAA;MACAc,OAAA,CAAAd,KAAA,SAAAA,KAAA;MACA,KAAA9B,WAAA,cAAA8B,KAAA,CAAAgP,OAAA;MACA,KAAA/Q,eAAA;IACA;EACA,QAAAlB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,mCAGAwT,qBAAAlB,OAAA;IACA,IAAA7G,SAAA;IACA,IAAAG,MAAA;IAEA,KAAA0G,OAAA,WAAAA,OAAA;MACA/M,OAAA,CAAA6I,IAAA;MACA;QAAA3C,SAAA,EAAAA,SAAA;QAAAG,MAAA;MAAA;IACA;IAEA;MACArG,OAAA,CAAAqL,GAAA,iBAAA0B,OAAA,CAAAzL,MAAA;;MAEA;MACA,IAAAuL,WAAA,QAAAjB,uBAAA,CAAAmB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAA5P,IAAA,GAAAqE,MAAA;QACAtB,OAAA,CAAA6I,IAAA;QACA;UAAA3C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;;MAEA;MACA,IAAA8H,KAAA,GAAAtB,WAAA,CAAAuB,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;QAAA,OAAAA,IAAA,CAAApR,IAAA;MAAA,GAAAqR,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAA/M,MAAA;MAAA;MACAtB,OAAA,CAAAqL,GAAA,SAAA8C,KAAA,CAAA7M,MAAA;MAEA,IAAA6M,KAAA,CAAA7M,MAAA;QACAtB,OAAA,CAAA6I,IAAA;QACA;UAAA3C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;MAEArG,OAAA,CAAAqL,GAAA,WAAA8C,KAAA,CAAAI,KAAA;MAEA,IAAAC,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAP,IAAA,UAAAQ,mBAAA,CAAAR,IAAA;QAEA,IAAAM,eAAA;UACA;UACA,IAAAH,oBAAA,CAAAlN,MAAA;YACA;cACA,IAAAwN,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACA/O,OAAA,CAAAqL,GAAA,0CAAA5J,MAAA,CAAAgN,cAAA,0BAAAK,YAAA;cACA,IAAAE,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBAAA,IAAAE,qBAAA,EAAAC,qBAAA;gBACAjJ,SAAA,CAAApD,IAAA,CAAAkM,cAAA;gBACAhP,OAAA,CAAAqL,GAAA,oCAAA5J,MAAA,CAAAyE,SAAA,CAAA5E,MAAA;kBACAP,IAAA,EAAAiO,cAAA,CAAApT,YAAA;kBACAmR,OAAA,IAAAmC,qBAAA,GAAAF,cAAA,CAAAlT,eAAA,cAAAoT,qBAAA,uBAAAA,qBAAA,CAAAlL,SAAA;kBACAoL,OAAA,IAAAD,qBAAA,GAAAH,cAAA,CAAAI,OAAA,cAAAD,qBAAA,uBAAAA,qBAAA,CAAA7N,MAAA;kBACA+N,MAAA,EAAAL,cAAA,CAAAM;gBACA;cACA;YACA,SAAApQ,KAAA;cACAmH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAgN,cAAA,uCAAAhN,MAAA,CAAAvC,KAAA,CAAAgP,OAAA;cACAlO,OAAA,CAAAd,KAAA,wBAAAuC,MAAA,CAAAgN,cAAA,iCAAAvP,KAAA;YACA;UACA;;UAEA;UACAsP,oBAAA,IAAAH,IAAA;UACAI,cAAA;UACAzO,OAAA,CAAAqL,GAAA,0CAAA5J,MAAA,CAAAgN,cAAA,QAAAJ,IAAA;QACA;UACA;UACA,IAAAG,oBAAA,CAAAlN,MAAA;YACAkN,oBAAA,CAAA1L,IAAA,CAAAuL,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAG,oBAAA,CAAAlN,MAAA;QACA;UACA,IAAAwN,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YAAA,IAAAO,sBAAA;YACArJ,SAAA,CAAApD,IAAA,CAAAkM,eAAA;YACAhP,OAAA,CAAAqL,GAAA,gDAAA5J,MAAA,CAAAyE,SAAA,CAAA5E,MAAA,UAAAiO,sBAAA,GAAAP,eAAA,CAAAlT,eAAA,cAAAyT,sBAAA,uBAAAA,sBAAA,CAAAvL,SAAA;UACA;QACA,SAAA9E,KAAA;UACAmH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAgN,cAAA,uCAAAhN,MAAA,CAAAvC,KAAA,CAAAgP,OAAA;UACAlO,OAAA,CAAAd,KAAA,oCAAAuC,MAAA,CAAAgN,cAAA,iCAAAvP,KAAA;QACA;MACA;IAEA,SAAAA,KAAA;MACAmH,MAAA,CAAAvD,IAAA,0CAAArB,MAAA,CAAAvC,KAAA,CAAAgP,OAAA;MACAlO,OAAA,CAAAd,KAAA,cAAAA,KAAA;IACA;IAEAc,OAAA,CAAAqL,GAAA,WAAAnF,SAAA,CAAA5E,MAAA,UAAA+E,MAAA,CAAA/E,MAAA;IACA;MAAA4E,SAAA,EAAAA,SAAA;MAAAG,MAAA,EAAAA;IAAA;EACA,oCAGAuI,oBAAAP,IAAA;IACA;IACA;IACA;IACA,wBAAAmB,IAAA,CAAAnB,IAAA;EACA,oCAGAQ,oBAAAR,IAAA;IACA;IACA;IACA,mBAAAmB,IAAA,CAAAnB,IAAA;EACA,uCAGAY,uBAAAH,YAAA;IACA,IAAAX,KAAA,GAAAW,YAAA,CAAAV,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;MAAA,OAAAA,IAAA,CAAApR,IAAA;IAAA,GAAAqR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA/M,MAAA;IAAA;IAEA,IAAA6M,KAAA,CAAA7M,MAAA;MACA,UAAAkH,KAAA;IACA;IAEA,IAAA5M,YAAA;IACA,IAAAE,eAAA;IACA,IAAA2T,iBAAA;;IAEA;IACA,SAAAf,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;MACA,IAAAgB,SAAA,GAAArB,IAAA,CAAAsB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;QACA1P,OAAA,CAAAqL,GAAA,eAAAuE,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAA/M,QAAA;UACAjH,YAAA;QACA,WAAAgU,QAAA,CAAA/M,QAAA;UACAjH,YAAA;QACA,WAAAgU,QAAA,CAAA/M,QAAA;UACAjH,YAAA;QACA,WAAAgU,QAAA,CAAA/M,QAAA;UACAjH,YAAA;QACA,WAAAgU,QAAA,CAAA/M,QAAA;UACAjH,YAAA;QACA;QAEAoE,OAAA,CAAAqL,GAAA,gBAAAzP,YAAA;;QAEA;QACA,IAAAiU,gBAAA,GAAAxB,IAAA,CAAAvK,OAAA,iBAAA7G,IAAA;QACA,IAAA4S,gBAAA;UACA/T,eAAA,GAAA+T,gBAAA;UACAJ,iBAAA,GAAAf,CAAA;UACA1O,OAAA,CAAAqL,GAAA,kBAAAwE,gBAAA;QACA;UACAJ,iBAAA,GAAAf,CAAA;UACA1O,OAAA,CAAAqL,GAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAoE,iBAAA;MACAA,iBAAA;MACA;MACA7T,YAAA,QAAAkU,iBAAA,CAAA3B,KAAA;MACAnO,OAAA,CAAAqL,GAAA,sBAAAzP,YAAA;IACA;;IAEA;IACA,SAAA8S,EAAA,GAAAe,iBAAA,EAAAf,EAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,EAAA;MACA,IAAAL,KAAA,GAAAF,KAAA,CAAAO,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAP,KAAA;QACA;QACAvS,eAAA,GAAAuS,KAAA,CAAAvK,OAAA,uBAAA7G,IAAA;QACAwS,iBAAA,GAAAf,EAAA;QACA;MACA,YAAA5S,eAAA;QACA;QACAA,eAAA,GAAAuS,KAAA;QACAoB,iBAAA,GAAAf,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAe,iBAAA,EAAAf,GAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,GAAA;MACA,IAAAL,MAAA,GAAAF,KAAA,CAAAO,GAAA;;MAEA;MACA,SAAAqB,YAAA,CAAA1B,MAAA,UAAA2B,YAAA,CAAA3B,MAAA,KACA,KAAA4B,iBAAA,CAAA5B,MAAA,UAAA6B,gBAAA,CAAA7B,MAAA;QACA;MACA;;MAEA;MACA,IAAA8B,SAAA,GAAA9B,MAAA;MACA;MACA,SAAAO,mBAAA,CAAAP,MAAA;QACA8B,SAAA,GAAA9B,MAAA,CAAAvK,OAAA,uBAAA7G,IAAA;MACA;MAEA,IAAAkT,SAAA;QACA,IAAArU,eAAA;UACAA,eAAA,WAAAqU,SAAA;QACA;UACArU,eAAA,GAAAqU,SAAA;QACA;MACA;IACA;IAEAnQ,OAAA,CAAAqL,GAAA,mEAAAvP,eAAA;IAEA,KAAAA,eAAA;MACA,UAAA0M,KAAA;IACA;;IAEA;IACA,IAAA4H,oBAAA,GAAAtU,eAAA,CAAAmB,IAAA;IACA;IACA,wBAAAuS,IAAA,CAAAY,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAAtM,OAAA,0BAAA7G,IAAA;IACA;;IAEA;IACA,IAAAmT,oBAAA,CAAAvN,QAAA;MACAuN,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAAhN,QAAA;MACAxH,YAAA,EAAAA,YAAA;MACAmF,IAAA,EAAAnF,YAAA;MACA0U,QAAA,OAAAC,kBAAA,CAAA3U,YAAA;MACAE,eAAA,EAAAsU,oBAAA;MACArD,OAAA,EAAAqD,oBAAA;MACAvU,UAAA;MAAA;MACA2U,WAAA;MACApB,OAAA;MACAE,aAAA;MACAnJ,SAAA;IACA;IAEAnG,OAAA,CAAAqL,GAAA;MACAtK,IAAA,EAAAnF,YAAA;MACA2K,eAAA,EAAAzK,eAAA,CAAAmB,IAAA;MACAwT,YAAA,EAAAL,oBAAA;MACAM,iBAAA,gBAAAlB,IAAA,CAAAY,oBAAA;IACA;;IAEA;IACA,IAAAO,YAAA,QAAAC,qBAAA,CAAAzC,KAAA;IACA/K,QAAA,CAAAgM,OAAA,GAAAuB,YAAA,CAAAvB,OAAA;;IAEA;IACA,IAAAxT,YAAA,mBAAAwH,QAAA,CAAAgM,OAAA,CAAA9N,MAAA;MACA;MACA1F,YAAA;MACAwH,QAAA,CAAAxH,YAAA,GAAAA,YAAA;MACAwH,QAAA,CAAArC,IAAA,GAAAnF,YAAA;MACAwH,QAAA,CAAAkN,QAAA,QAAAC,kBAAA,CAAA3U,YAAA;MACAoE,OAAA,CAAAqL,GAAA,oBAAAzP,YAAA;IACA;;IAEA;IACA,KAAAiV,0BAAA,CAAA1C,KAAA,EAAA/K,QAAA;;IAEA;IACA,IAAAxH,YAAA,iBAAAwH,QAAA,CAAAkM,aAAA,IAAAlM,QAAA,CAAAkM,aAAA,CAAAhO,MAAA;MACA;MACA,kBAAAkO,IAAA,CAAApM,QAAA,CAAAkM,aAAA;QACA1T,YAAA;QACAwH,QAAA,CAAAxH,YAAA,GAAAA,YAAA;QACAwH,QAAA,CAAArC,IAAA,GAAAnF,YAAA;QACAwH,QAAA,CAAAkN,QAAA,QAAAC,kBAAA,CAAA3U,YAAA;QACAoE,OAAA,CAAAqL,GAAA,sBAAAzP,YAAA;MACA;IACA;;IAEA;IACAwH,QAAA,CAAAtH,eAAA,QAAAuU,oBAAA,CAAAjN,QAAA,CAAAtH,eAAA;IACAsH,QAAA,CAAA2J,OAAA,GAAA3J,QAAA,CAAAtH,eAAA;IAEA,OAAAsH,QAAA;EACA,6BAGA2M,aAAA1B,IAAA;IACA;IACA,6BAAAmB,IAAA,CAAAnB,IAAA;EACA,6BAGA2B,aAAA3B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,kCAGA4B,kBAAA5B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,iCAGA6B,iBAAA7B,IAAA;IACA;IACA,sBAAAmB,IAAA,CAAAnB,IAAA;EACA,mCAGAkC,mBAAAxP,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,kCAGA+P,kBAAA/D,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA/M,OAAA,CAAAqL,GAAA,qBAAA0B,OAAA,CAAA/I,SAAA;;MAEA;MACA,IAAA+M,gBAAA,GAAAhE,OAAA,CAAAjJ,OAAA,mDAAA6L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAAtB,KAAA;QAEA3P,OAAA,CAAAqL,GAAA,mBAAA4F,GAAA;;QAEA;QACA,IAAAA,GAAA,CAAAjG,UAAA,eAAAiG,GAAA,CAAAjG,UAAA,gBAAAiG,GAAA,CAAAjG,UAAA;UACA,OAAA2E,KAAA;QACA;;QAEA;QACA,IAAAwB,OAAA,8BAAAF,GAAA,CAAAjG,UAAA,QAAAiG,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAA3P,MAAA,CAAAuP,MAAA,YAAAvP,MAAA,CAAA0P,OAAA,QAAA1P,MAAA,CAAAyP,KAAA;QACAlR,OAAA,CAAAqL,GAAA,kBAAA+F,MAAA;QACA,OAAAA,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAA7R,KAAA;MACAc,OAAA,CAAAd,KAAA,iBAAAA,KAAA;MACA,OAAA6N,OAAA;IACA;EACA,QAAA9Q,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yCAGAkR,2BAAAoB,OAAA;IAAA,IAAAsE,OAAA;IACA,KAAAtE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAAgE,gBAAA,GAAAhE;MACA;MAAA,CACAjJ,OAAA,oDAAA6L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAAjG,UAAA,aAAAiG,GAAA,CAAAjG,UAAA;UACA,IAAAmG,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAAxP,MAAA,CAAAuP,MAAA,YAAAvP,MAAA,CAAA0P,OAAA,QAAA1P,MAAA,CAAAyP,KAAA;QACA;QACA,OAAAvB,KAAA;MACA;MACA;MAAA,CACA7L,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAAiN,gBAAA,CAAA9T,IAAA;IACA,SAAAiC,KAAA;MACAc,OAAA,CAAAd,KAAA,qCAAAA,KAAA;MACA,OAAA6N,OAAA;IACA;EACA,wCAGAnB,wBAAAmB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA/M,OAAA,CAAAqL,GAAA,sCAAA0B,OAAA,CAAAzL,MAAA;;MAEA;MACA,IAAAgQ,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAAzE,OAAA,CAAAjJ,OAAA,2BAAA6L,KAAA;QACA3P,OAAA,CAAAqL,GAAA,gBAAAsE,KAAA,CAAA3L,SAAA;QACAsN,MAAA,CAAAxO,IAAA,CAAA6M,KAAA;QACA,gCAAAlO,MAAA,CAAA8P,UAAA;MACA;;MAEA;MACA,IAAA1E,WAAA,GAAA2E,uBAAA,CACA1N,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAA2M,YAAA,GAAA5D,WAAA;MACAyE,MAAA,CAAA/Q,OAAA,WAAAkR,GAAA,EAAA1O,KAAA;QACA,IAAAqJ,WAAA,0BAAA3K,MAAA,CAAAsB,KAAA;QACA,IAAA0N,YAAA,CAAA5N,QAAA,CAAAuJ,WAAA;UACAqE,YAAA,GAAAA,YAAA,CAAA3M,OAAA,CAAAsI,WAAA,EAAAqF,GAAA;QACA;MACA;MAEAzR,OAAA,CAAAqL,GAAA,yCAAAoF,YAAA,CAAAnP,MAAA;MACA,OAAAmP,YAAA,CAAAxT,IAAA;IACA,SAAAiC,KAAA;MACAc,OAAA,CAAAd,KAAA,kCAAAA,KAAA;MACA,OAAA6N,OAAA;IACA;EACA,sCAGA6D,sBAAAzC,KAAA,EAAAuD,UAAA;IACA,IAAAtC,OAAA;IAEA,KAAA7B,KAAA,CAAAoE,OAAA,CAAAxD,KAAA,KAAAuD,UAAA,QAAAA,UAAA,IAAAvD,KAAA,CAAA7M,MAAA;MACAtB,OAAA,CAAA6I,IAAA;MACA;QAAAuG,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAAV,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;QAEA,KAAAL,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAuD,WAAA,GAAAvD,IAAA,CAAAsB,KAAA;QACA,IAAAiC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAA3U,IAAA;UAEA,IAAA4U,SAAA,IAAAE,aAAA;YACA3C,OAAA,CAAAtM,IAAA;cACA+O,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACAhF,OAAA,EAAAgF;YACA;UACA;QACA,gBAAA/B,YAAA,CAAA3B,IAAA,UAAA4B,iBAAA,CAAA5B,IAAA,UAAA6B,gBAAA,CAAA7B,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAA4D,oBAAA,GAAA5D,IAAA,CAAAsB,KAAA;UACA,IAAAsC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAA7D,IAAA,CAAAD,KAAA;YAAA,IAAA+D,SAAA,OAAAC,2BAAA,CAAAlW,OAAA,EACAgW,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAjK,CAAA,IAAA9D,IAAA;gBAAA,IAAAmO,YAAA,GAAAF,KAAA,CAAAhG,KAAA;gBACA,KAAAkG,YAAA;gBAEA,IAAA5C,KAAA,GAAA4C,YAAA,CAAA5C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAkC,UAAA,GAAAlC,KAAA,IAAAmC,WAAA;kBACA,IAAAC,cAAA,GAAApC,KAAA,MAAAA,KAAA,IAAA1S,IAAA;kBAEA,IAAA4U,UAAA,IAAAE,cAAA;oBACA3C,OAAA,CAAAtM,IAAA;sBACA+O,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACAhF,OAAA,EAAAgF;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAAhH,CAAA,CAAAqH,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAAvT,KAAA;MACAc,OAAA,CAAAd,KAAA,eAAAA,KAAA;IACA;IAEA;MAAAkQ,OAAA,EAAAA;IAAA;EACA,2CAGAyB,2BAAA1C,KAAA,EAAA/K,QAAA;IACA,SAAAsL,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA,IAAAgE,WAAA,GAAArE,IAAA,CAAAsB,KAAA;MACA,IAAA+C,WAAA;QACAtP,QAAA,CAAAkM,aAAA,QAAAqD,gBAAA,CAAAD,WAAA,KAAAtP,QAAA,CAAAxH,YAAA;QACA;MACA;;MAEA;MACA,IAAAgX,gBAAA,GAAAvE,IAAA,CAAAsB,KAAA;MACA,IAAAiD,gBAAA;QACAxP,QAAA,CAAAoN,WAAA,GAAAoC,gBAAA,IAAA3V,IAAA;QACA;MACA;;MAEA;MACA,IAAA4V,eAAA,GAAAxE,IAAA,CAAAsB,KAAA;MACA,IAAAkD,eAAA;QACA,IAAAhX,UAAA,GAAAgX,eAAA;QACA;QACA,IAAAhX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAgH,QAAA,CAAAhH,UAAA;UACAuH,QAAA,CAAAvH,UAAA,GAAAA,UAAA;UACAmE,OAAA,CAAAqL,GAAA,cAAAxP,UAAA;QACA;UACAmE,OAAA,CAAA6I,IAAA,iBAAAhN,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAAuH,QAAA,CAAAkM,aAAA;MACAlM,QAAA,CAAAkM,aAAA,QAAAwD,gCAAA,CAAA1P,QAAA,CAAAtH,eAAA,EAAAsH,QAAA,CAAAxH,YAAA;IACA;EACA,iDAGAkX,iCAAAhX,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAAiX,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAA3R,MAAA,EAAA0R,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAArX,eAAA,CAAA6T,KAAA,CAAAuD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAA7R,MAAA;UACA;UACA,IAAA8R,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAA7R,MAAA;UACA,IAAA+N,MAAA,GAAA+D,SAAA,CAAAtP,OAAA,sBAAA7G,IAAA;UAEA,IAAAoS,MAAA;YACA,YAAAsD,gBAAA,CAAAtD,MAAA,EAAAzT,YAAA;UACA;QACA;MACA;IACA,SAAAsD,KAAA;MACAc,OAAA,CAAAd,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGAyT,iBAAAU,UAAA,EAAAzX,YAAA;IACA,KAAAyX,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAApW,IAAA;MAEA,KAAAqW,aAAA;QACA;MACA;MAEA,IAAA1X,YAAA;QACA;QACA,OAAA0X,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAxB,WAAA;MACA;IACA,SAAA5S,KAAA;MACAc,OAAA,CAAAd,KAAA,gBAAAA,KAAA;MACA,OAAAmU,UAAA;IACA;EACA,oCAGAE,oBAAAxG,OAAA;IACA,IAAAyG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAA/D,KAAA;IACA,IAAAgE,WAAA;IAEA,QAAAhE,KAAA,GAAA8D,SAAA,CAAAG,IAAA,CAAA7G,OAAA;MACA,IAAA4G,WAAA;QACA;QACAH,QAAA,CAAA1Q,IAAA;UACA/B,IAAA,EAAA4S,WAAA;UACA5G,OAAA,EAAAA,OAAA,CAAA/I,SAAA,CAAA0P,SAAA,EAAA/D,KAAA,CAAA5M,KAAA,EAAA9F,IAAA;QACA;MACA;MACA0W,WAAA,GAAAhE,KAAA;MACA+D,SAAA,GAAA/D,KAAA,CAAA5M,KAAA,GAAA4M,KAAA,IAAArO,MAAA;IACA;;IAEA;IACA,IAAAqS,WAAA;MACAH,QAAA,CAAA1Q,IAAA;QACA/B,IAAA,EAAA4S,WAAA;QACA5G,OAAA,EAAAA,OAAA,CAAA/I,SAAA,CAAA0P,SAAA,EAAAzW,IAAA;MACA;IACA;IAEA,OAAAuW,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAA7N,SAAA;IACA,IAAAtK,YAAA,QAAAoY,mBAAA,CAAAF,OAAA,CAAA/S,IAAA;;IAEA;IACA,IAAAkT,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA/G,OAAA;IAEAkH,cAAA,CAAA1T,OAAA,WAAA4T,KAAA,EAAApR,KAAA;MACA;QACA,IAAAK,QAAA,GAAA2Q,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAAvY,YAAA,EAAAmH,KAAA;QACA,IAAAK,QAAA;UACA8C,SAAA,CAAApD,IAAA,CAAAM,QAAA;QACA;MACA,SAAAlE,KAAA;QACA,UAAAsJ,KAAA,UAAA/G,MAAA,CAAAsB,KAAA,0CAAAtB,MAAA,CAAAvC,KAAA,CAAAgP,OAAA;MACA;IACA;IAEA,OAAAhI,SAAA;EACA,sCAGAgO,sBAAAnH,OAAA;IACA,IAAAsH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAA/D,KAAA;IAEA,QAAAA,KAAA,GAAA2E,WAAA,CAAAV,IAAA,CAAA7G,OAAA;MACA,IAAA2G,SAAA;QACA;QACAW,MAAA,CAAAvR,IAAA,CAAAiK,OAAA,CAAA/I,SAAA,CAAA0P,SAAA,EAAA/D,KAAA,CAAA5M,KAAA,EAAA9F,IAAA;MACA;MACAyW,SAAA,GAAA/D,KAAA,CAAA5M,KAAA;IACA;;IAEA;IACA,IAAA2Q,SAAA,GAAA3G,OAAA,CAAAzL,MAAA;MACA+S,MAAA,CAAAvR,IAAA,CAAAiK,OAAA,CAAA/I,SAAA,CAAA0P,SAAA,EAAAzW,IAAA;IACA;IAEA,OAAAoX,MAAA,CAAA/F,MAAA,WAAA6F,KAAA;MAAA,OAAAA,KAAA,CAAA7S,MAAA;IAAA;EACA,mCAGA8S,mBAAAD,KAAA,EAAAvY,YAAA;IACA,IAAAuS,KAAA,GAAAgG,KAAA,CAAA/F,KAAA,OAAAxM,GAAA,WAAAyM,IAAA;MAAA,OAAAA,IAAA,CAAApR,IAAA;IAAA,GAAAqR,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA/M,MAAA;IAAA;IAEA,IAAA6M,KAAA,CAAA7M,MAAA;MACA,UAAAkH,KAAA;IACA;;IAEA;IACA,IAAA+L,SAAA,GAAApG,KAAA;IACA,IAAArS,eAAA;IACA,IAAA0Y,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA5E,KAAA;IACA,IAAA8E,WAAA;MACA3Y,eAAA,GAAA2Y,WAAA,IAAAxX,IAAA;MACAuX,gBAAA;IACA;MACA;MACA1Y,eAAA,QAAAuU,oBAAA,CAAAkE,SAAA,EAAAtX,IAAA;MACAuX,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAArG,KAAA,CAAA7M,MAAA;MACA,IAAA+M,IAAA,GAAAF,KAAA,CAAAqG,gBAAA;MACA,SAAAzE,YAAA,CAAA1B,IAAA;QACA;MACA;MACAvS,eAAA,WAAAuS,IAAA;MACAmG,gBAAA;IACA;IAEA,IAAApR,QAAA;MACAxH,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAmB,IAAA;MACApB,UAAA;MAAA;MACA2U,WAAA;MACApB,OAAA;MACAE,aAAA;IACA;;IAEA;IACA,IAAA1T,YAAA;MACA,IAAA+U,YAAA,QAAA+D,YAAA,CAAAvG,KAAA,EAAAqG,gBAAA;MACApR,QAAA,CAAAgM,OAAA,GAAAuB,YAAA,CAAAvB,OAAA;MACAoF,gBAAA,GAAA7D,YAAA,CAAAgE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAzG,KAAA,EAAAqG,gBAAA,EAAApR,QAAA;;IAEA;IACAA,QAAA,CAAAtH,eAAA,QAAAuU,oBAAA,CAAAjN,QAAA,CAAAtH,eAAA;IAEA,OAAAsH,QAAA;EACA,QAAAnH,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,2BAAAsV,aAGA1B,IAAA;IACA,4BAAAmB,IAAA,CAAAnB,IAAA;EACA,6BAGAqG,aAAAvG,KAAA,EAAAuD,UAAA;IACA,IAAAtC,OAAA;IACA,IAAAyF,YAAA,GAAAnD,UAAA;IAEA,OAAAmD,YAAA,GAAA1G,KAAA,CAAA7M,MAAA;MACA,IAAA+M,IAAA,GAAAF,KAAA,CAAA0G,YAAA;MACA,IAAAjD,WAAA,GAAAvD,IAAA,CAAAsB,KAAA;MAEA,KAAAiC,WAAA;QACA;MACA;MAEAxC,OAAA,CAAAtM,IAAA;QACA+O,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAA3U,IAAA;MACA;MAEA4X,YAAA;IACA;IAEA;MAAAzF,OAAA,EAAAA,OAAA;MAAAuF,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAzG,KAAA,EAAAuD,UAAA,EAAAtO,QAAA;IACA,SAAAsL,CAAA,GAAAgD,UAAA,EAAAhD,CAAA,GAAAP,KAAA,CAAA7M,MAAA,EAAAoN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA,IAAAgE,WAAA,GAAArE,IAAA,CAAAsB,KAAA;MACA,IAAA+C,WAAA;QACAtP,QAAA,CAAAkM,aAAA,QAAAwF,WAAA,CAAApC,WAAA,KAAAtP,QAAA,CAAAxH,YAAA;QACA;MACA;;MAEA;MACA,IAAAgX,gBAAA,GAAAvE,IAAA,CAAAsB,KAAA;MACA,IAAAiD,gBAAA;QACAxP,QAAA,CAAAoN,WAAA,GAAAoC,gBAAA,IAAA3V,IAAA;QACA;MACA;;MAEA;MACA,IAAA4V,eAAA,GAAAxE,IAAA,CAAAsB,KAAA;MACA,IAAAkD,eAAA;QACA,IAAAhX,UAAA,GAAAgX,eAAA;QACA;QACA,IAAAhX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAgH,QAAA,CAAAhH,UAAA;UACAuH,QAAA,CAAAvH,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAAuH,QAAA,CAAAkM,aAAA;MACAlM,QAAA,CAAAkM,aAAA,QAAAyF,wBAAA,CAAA3R,QAAA,CAAAtH,eAAA,EAAAsH,QAAA,CAAAxH,YAAA;IACA;EACA,yCAKAmZ,yBAAAhI,OAAA,EAAAnR,YAAA;IACA;IACA,IAAAoZ,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAA5T,MAAA,EAAA2T,GAAA;MAAA,IAAA/B,OAAA,GAAAgC,gBAAA,CAAAD,GAAA;MACA,IAAA9B,OAAA,OAAA/K,mBAAA,CAAAlM,OAAA,EAAA6Q,OAAA,CAAAoI,QAAA,CAAAjC,OAAA;MACA,IAAAC,OAAA,CAAA7R,MAAA;QACA,IAAA+N,MAAA,GAAA8D,OAAA,CAAAA,OAAA,CAAA7R,MAAA;QACA,YAAAwT,WAAA,CAAAzF,MAAA,EAAAzT,YAAA;MACA;IACA;IAEA;EACA,oCAGAoY,oBAAApE,QAAA;IACA,IAAAzP,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAyP,QAAA;EACA,oCAGAwF,oBAAArU,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,qCAKAsU,qBAAAtU,IAAA;IACA,IAAAuU,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAAvU,IAAA;EACA,kCAKA3C,kBAAA;IACA;MACA,IAAAmX,UAAA,GAAAC,YAAA,CAAAC,OAAA,MAAAC,QAAA;MACA,IAAAH,UAAA;QACA,IAAAxa,IAAA,GAAA4a,IAAA,CAAAC,KAAA,CAAAL,UAAA;QACAvV,OAAA,CAAAqL,GAAA,eAAAtQ,IAAA;;QAEA;QACA,KAAA8B,eAAA,GAAA9B,IAAA,CAAA8B,eAAA;QACA,KAAA4J,mBAAA,GAAA1L,IAAA,CAAA0L,mBAAA;QACA,KAAAC,YAAA,GAAA3L,IAAA,CAAA2L,YAAA;;QAEA;QACA,SAAA7J,eAAA,SAAA4J,mBAAA;UACA,KAAAoF,iBAAA;UACA7L,OAAA,CAAAqL,GAAA;QACA;MACA;IACA,SAAAnM,KAAA;MACAc,OAAA,CAAAd,KAAA,gBAAAA,KAAA;IACA;EACA,4BAGA4M,YAAA;IAAA,IAAA+J,OAAA;IACA;IACA,SAAAnX,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;IAEA,KAAAA,aAAA,GAAAuH,UAAA;MACA4P,OAAA,CAAApX,cAAA;IACA;EACA,+BAGAA,eAAA;IACA;MACA,IAAAqX,UAAA;QACAjZ,eAAA,OAAAA,eAAA;QACA4J,mBAAA,OAAAA,mBAAA;QACAC,YAAA,OAAAA,YAAA,QAAAC,IAAA,GAAAC,cAAA;QACAmP,SAAA,EAAApP,IAAA,CAAAqP,GAAA;MACA;MAEAR,YAAA,CAAAS,OAAA,MAAAP,QAAA,EAAAC,IAAA,CAAAO,SAAA,CAAAJ,UAAA;MACA9V,OAAA,CAAAqL,GAAA;MACA,KAAAQ,iBAAA;IACA,SAAA3M,KAAA;MACAc,OAAA,CAAAd,KAAA,eAAAA,KAAA;IACA;EACA,QAAAjD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yBAGAgO,WAAA;IACA;MACA+M,YAAA,CAAAW,UAAA,MAAAT,QAAA;MACA,KAAA7J,iBAAA;MACA7L,OAAA,CAAAqL,GAAA;IACA,SAAAnM,KAAA;MACAc,OAAA,CAAAd,KAAA,cAAAA,KAAA;IACA;EACA,mCAGAX,mBAAA6X,KAAA;IACA,SAAAvK,iBAAA;MACA;MACA,KAAApN,cAAA;;MAEA;MACA,IAAAyP,OAAA;MACAkI,KAAA,CAAAC,WAAA,GAAAnI,OAAA;MACA,OAAAA,OAAA;IACA;EACA,2BAGAoI,WAAA;IACA,KAAA7X,cAAA;IACA,KAAAQ,QAAA,CAAA8C,OAAA;EACA,mCAGAwU,mBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,QAAAD,OAAA;MACA;QACA,KAAAtU,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACArB,IAAA;QACA,GAAAnB,IAAA;UACA6W,OAAA,CAAAhO,UAAA;UACAgO,OAAA,CAAA5Z,eAAA;UACA4Z,OAAA,CAAAhQ,mBAAA;UACAgQ,OAAA,CAAA/P,YAAA;;UAEA;UACA,IAAA+P,OAAA,CAAA/Y,UAAA,IAAA+Y,OAAA,CAAA7Y,iBAAA;YACA6Y,OAAA,CAAA/Y,UAAA,CAAA0H,OAAA;UACA;UAEAqR,OAAA,CAAAxX,QAAA,CAAA8C,OAAA;QACA,GAAAhC,KAAA;UACA;QAAA,CACA;QACA;MACA;QACA,KAAA2W,WAAA;QACA;IACA;EACA,4BAGAA,YAAA;IACA,UAAAjQ,mBAAA,UAAA5J,eAAA;MACA,KAAAoC,QAAA,CAAAsC,OAAA;MACA;IACA;IAEA,IAAAwL,OAAA,QAAAtG,mBAAA,SAAA5J,eAAA;IACA,IAAA8Z,IAAA,OAAAC,IAAA,EAAA7J,OAAA;MAAAhM,IAAA;IAAA;IACA,IAAA8V,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;IACA,IAAAK,IAAA,GAAAhO,QAAA,CAAAkD,aAAA;IACA8K,IAAA,CAAAC,IAAA,GAAAJ,GAAA;IACAG,IAAA,CAAA1R,QAAA,+BAAA7D,MAAA,KAAAkF,IAAA,GAAAuQ,WAAA,GAAA3I,KAAA,QAAAzK,OAAA;IACAkF,QAAA,CAAAmO,IAAA,CAAA1K,WAAA,CAAAuK,IAAA;IACAA,IAAA,CAAAI,KAAA;IACApO,QAAA,CAAAmO,IAAA,CAAAE,WAAA,CAAAL,IAAA;IACAF,GAAA,CAAAQ,eAAA,CAAAT,GAAA;IAEA,KAAA5X,QAAA,CAAA8C,OAAA;EACA,4CAKAwV,4BAAAnU,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAAtH,eAAA;MACA;IACA;IAEA,IAAAiR,OAAA,GAAA3J,QAAA,CAAAtH,eAAA;;IAEA;IACA,SAAA2K,mBAAA,SAAAA,mBAAA,CAAA5D,QAAA;MACA;MACA,IAAA2U,WAAA,QAAAC,uBAAA,CAAArU,QAAA,CAAAtH,eAAA,OAAA2K,mBAAA;MACA,IAAA+Q,WAAA;QACAzK,OAAA,GAAAyK,WAAA;MACA;IACA;;IAEA;IACAzK,OAAA,QAAAsD,oBAAA,CAAAtD,OAAA;;IAEA;IACAA,OAAA,QAAA2K,sBAAA,CAAA3K,OAAA;IAEA,YAAA+D,iBAAA,CAAA/D,OAAA;EACA,uCAGA2K,uBAAA3K,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAAlK,QAAA;MACA,OAAAkK;MACA;MAAA,CACAjJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACA7G,IAAA;IACA;MACA;MACA,OAAA8P;MACA;MAAA,CACAjJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACA7G,IAAA;IACA;EACA,qCAGAoT,qBAAAtD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAAlK,QAAA;MACA;MACA,OAAAkK,OAAA,CAAAjJ,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAAiJ,OAAA,CAAAjJ,OAAA;MAAA,CACAA,OAAA;MAAA,CACA7G,IAAA;IACA;EACA,wCAGAwa,wBAAAE,YAAA,EAAAH,WAAA;IACA,KAAAG,YAAA,KAAAH,WAAA;MACA,OAAAG,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAA7T,OAAA,uBAAA7G,IAAA;;MAEA;MACA,IAAA4a,UAAA,GAAAL,WAAA,CAAA7H,KAAA;MAAA,IAAAmI,UAAA,OAAA1F,2BAAA,CAAAlW,OAAA,EAEA2b,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxF,CAAA,MAAAyF,MAAA,GAAAD,UAAA,CAAA5P,CAAA,IAAA9D,IAAA;UAAA,IAAA4T,SAAA,GAAAD,MAAA,CAAA1L,KAAA;UACA,IAAA4L,aAAA,GAAAD,SAAA,CAAAlU,OAAA,iBAAA7G,IAAA;UACA;UACA,IAAAib,kBAAA,GAAAD,aAAA,CAAAnU,OAAA,0BAAA7G,IAAA;UACA,IAAAib,kBAAA,CAAArV,QAAA,CAAA+U,SAAA,CAAA5T,SAAA;YACA;YACA,YAAAqM,oBAAA,CAAA2H,SAAA;UACA;QACA;;QAEA;MAAA,SAAAxF,GAAA;QAAAsF,UAAA,CAAA3M,CAAA,CAAAqH,GAAA;MAAA;QAAAsF,UAAA,CAAArF,CAAA;MAAA;MACA,OAAAkF,YAAA;IACA,SAAAzY,KAAA;MACAc,OAAA,CAAAd,KAAA,kBAAAA,KAAA;MACA,OAAAyY,YAAA;IACA;EACA,6BAIAQ,aAAA;IACA,KAAA1c,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA,QAAAnD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,0BAEA2d,YAAA;IACA,KAAA3c,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA;AAEA", "ignoreList": []}]}